{"name": "nextya", "private": true, "version": "1.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "format": "prettier --write .", "lint": "prettier --check . && eslint .", "test": "npm run format && npm run lint && npm run check", "db:migrate": "npx tsx database/dev/migrate.ts migrate", "db:rollback": "npx tsx database/dev/migrate.ts rollback", "db:generate": "kysely-codegen --config-file kysely.config.ts", "db:create": "npx tsx database/dev/migrate.ts create", "db:reset": "npx tsx database/dev/migrate.ts reset", "sql:migrate": "npx tsx database/dev/sql-migrator.ts migrate", "sql:generate": "npx tsx database/dev/sql-migrator.ts generate", "sql:list": "npx tsx database/dev/sql-migrator.ts list", "setup:init": "npx tsx database/dev/setup.ts setup", "setup:reset": "npx tsx database/dev/setup.ts reset", "setup:status": "npx tsx database/dev/setup.ts status"}, "devDependencies": {"@eslint/compat": "^1.3.0", "@eslint/js": "^9.31.0", "@sveltejs/adapter-node": "^5.2.13", "@sveltejs/kit": "^2.22.5", "@sveltejs/vite-plugin-svelte": "^6.0.0", "@types/bcryptjs": "^3.0.0", "@types/cookie": "^1.0.0", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^24.0.8", "@types/pg": "^8.15.4", "eslint": "^9.31.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-svelte": "^3.10.1", "globals": "^16.3.0", "kysely-codegen": "^0.18.5", "prettier": "^3.6.2", "prettier-plugin-svelte": "^3.4.0", "svelte": "^5.35.6", "svelte-check": "^4.2.2", "typescript": "^5.8.3", "typescript-eslint": "^8.36.0", "vite": "^7.0.4"}, "dependencies": {"@tailwindcss/vite": "^4.1.11", "@u4/opencv4nodejs": "^7.1.2", "bcryptjs": "^3.0.2", "chart.js": "^4.5.0", "cookie": "^1.0.2", "daisyui": "^5.0.46", "express": "^5.1.0", "fast-csv": "^5.0.2", "jsonwebtoken": "^9.0.2", "kysely": "^0.28.2", "lucide-svelte": "^0.522.0", "pg": "^8.16.3", "tailwindcss": "^4.1.10", "zod": "^4.0.5"}}